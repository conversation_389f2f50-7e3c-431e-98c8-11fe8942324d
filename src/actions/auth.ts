import {
  ForgotPasswordData,
  LoginData,
  RegisterData,
  ResetPasswordData,
  VerifyEmailData,
} from "@/stores/auth-store"
import { API_BASE_URL } from "."

// Get token from localStorage (client-side only)
const getToken = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("token")
  }
  return null
}

// Register user
export const registerUser = async (data: RegisterData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: data.email,
        username: data.username,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
      }),
    })

    const responseData = await response.json()
    return responseData
  } catch (error) {
    console.error("Registration error:", error)
    return { success: false, message: "Registration failed. Please try again." }
  }
}

// Login user
export const loginUser = async (data: LoginData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email: data.email, password: data.password }),
    })

    const responseData = await response.json()
    return responseData
  } catch (error) {
    console.error("Login error:", error)
    return { success: false, message: "Login failed. Please try again." }
  }
}

// Logout user
export const logoutUser = async () => {
  try {
    const token = getToken()
    if (!token) return { success: true }

    const response = await fetch(`${API_BASE_URL}/auth/logout`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Logout error:", error)
    return { success: false, message: "Logout failed." }
  }
}

// Get current user
export const getCurrentUser = async () => {
  try {
    const token = getToken()
    if (!token) return { success: false, message: "No token found" }

    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Get current user error:", error)
    return { success: false, message: "Failed to get user data." }
  }
}

// Verify email
export const verifyEmail = async (data: VerifyEmailData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/verify-email`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token: data.token }),
    })

    const responseData = await response.json()
    return responseData
  } catch (error) {
    console.error("Email verification error:", error)
    return {
      success: false,
      message: "Email verification failed. Please try again.",
    }
  }
}

// Forgot password
export const forgotPassword = async (data: ForgotPasswordData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email: data.email }),
    })

    const responseData = await response.json()
    return responseData
  } catch (error) {
    console.error("Forgot password error:", error)
    return {
      success: false,
      message: "Failed to send reset email. Please try again.",
    }
  }
}

// Reset password
export const resetPassword = async (data: ResetPasswordData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token: data.token, password: data.password }),
    })

    const responseData = await response.json()
    return responseData
  } catch (error) {
    console.error("Reset password error:", error)
    return {
      success: false,
      message: "Password reset failed. Please try again.",
    }
  }
}

// Google OAuth login
export const googleLogin = () => {
  window.location.href = `${API_BASE_URL}/auth/google`
}

// Handle Google OAuth callback
export const handleGoogleCallback = async (code: string) => {
  try {
    const response = await fetch(
      `${API_BASE_URL}/auth/google/callback?code=${code}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      },
    )

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Google callback error:", error)
    return {
      success: false,
      message: "Google authentication failed. Please try again.",
    }
  }
}

// Check if user has dashboard access
export const checkDashboardAccess = async () => {
  try {
    const token = getToken()
    if (!token) return { success: false, access: false }

    const response = await fetch(`${API_BASE_URL}/auth/dashboard-access`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Dashboard access check error:", error)
    return { success: false, access: false }
  }
}
