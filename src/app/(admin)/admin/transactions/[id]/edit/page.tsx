"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { <PERSON><PERSON>ef<PERSON>, RefreshCw } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { toast } from "sonner"

import { fetchTransactionById, updateTransaction } from "@/actions/transactions"
import { TransactionForm } from "@/components/forms/transaction-form"
import { Button } from "@/components/ui/button"
import { Container } from "@/components/layout"
import { TransactionFormData } from "@/types/transaction"

const EditTransactionPage = () => {
  const params = useParams()
  const router = useRouter()
  const transactionId = params.id as string

  const {
    data: transactionData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["transaction", transactionId],
    queryFn: () => fetchTransactionById(transactionId),
    enabled: !!transactionId,
  })

  const updateTransactionMutation = useMutation({
    mutationFn: (data: Partial<TransactionFormData>) =>
      updateTransaction(transactionId, data),
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Transaction updated successfully!")
        router.push(`/admin/transactions/${transactionId}`)
      } else {
        toast.error(data.message || "Failed to update transaction")
      }
    },
    onError: (error) => {
      console.error("Error updating transaction:", error)
      toast.error("Failed to update transaction")
    },
  })

  const handleSubmit = (data: TransactionFormData) => {
    updateTransactionMutation.mutate(data)
  }

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="text-center">
          <p>Loading transaction details...</p>
        </div>
      </Container>
    )
  }

  if (error || !transactionData?.data) {
    return (
      <Container className="py-8">
        <div className="text-center">
          <p className="text-red-600">Error loading transaction details</p>
          <Button onClick={() => refetch()} className="mt-4">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </div>
      </Container>
    )
  }

  const transaction = transactionData.data

  // Convert transaction data to form data format
  const initialData: TransactionFormData = {
    user: transaction.user,
    type: transaction.type,
    status: transaction.status,
    amount: transaction.amount,
    currency: transaction.currency,
    originalAmount: transaction.originalAmount,
    discountAmount: transaction.discountAmount,
    paymentMethod: transaction.paymentMethod,
    paymentGateway: transaction.paymentGateway,
    gatewayTransactionId: transaction.gatewayTransactionId,
    gatewayResponse: transaction.gatewayResponse,
    plan: transaction.plan,
    coupon: transaction.coupon,
    description: transaction.description,
    internalNotes: transaction.internalNotes,
    refund: transaction.refund,
    processedAt: transaction.processedAt,
    failedAt: transaction.failedAt,
    expiresAt: transaction.expiresAt,
    parentTransaction: transaction.parentTransaction,
    childTransactions: transaction.childTransactions,
    metadata: transaction.metadata,
  }

  return (
    <Container className="py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href={`/admin/transactions/${transactionId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Transaction
          </Link>
        </Button>
        
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Transaction</h1>
          <p className="text-muted-foreground">
            Transaction ID: {transactionId}
          </p>
        </div>
      </div>

      <div className="max-w-4xl">
        <TransactionForm
          initialData={initialData}
          onSubmit={handleSubmit}
        />
      </div>
    </Container>
  )
}

export default EditTransactionPage
