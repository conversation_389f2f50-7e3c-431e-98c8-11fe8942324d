"use client"

import { useQuery } from "@tanstack/react-query"
import { format } from "date-fns"
import {
  ArrowLeft,
  Copy,
  CreditCard,
  DollarSign,
  FileText,
  RefreshC<PERSON>,
  User,
} from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"

import { fetchTransactionById } from "@/actions/transactions"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Container } from "@/components/layout"

// Status badge colors
const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
    case "pending":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
    case "failed":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
    case "refunded":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
  }
}

// Type badge colors
const getTypeColor = (type: string) => {
  switch (type) {
    case "purchase":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
    case "refund":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
    case "upgrade":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
  }
}

const TransactionDetailPage = () => {
  const params = useParams()
  const transactionId = params.id as string

  const {
    data: transactionData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["transaction", transactionId],
    queryFn: () => fetchTransactionById(transactionId),
    enabled: !!transactionId,
  })

  const transaction = transactionData?.data

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="text-center">
          <p>Loading transaction details...</p>
        </div>
      </Container>
    )
  }

  if (error || !transaction) {
    return (
      <Container className="py-8">
        <div className="text-center">
          <p className="text-red-600">Error loading transaction details</p>
          <Button onClick={() => refetch()} className="mt-4">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </div>
      </Container>
    )
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <Container className="py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/admin/transactions">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Transactions
          </Link>
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transaction Details</h1>
            <p className="text-muted-foreground">
              Transaction ID: {transaction._id}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(transaction._id || "")}
            >
              <Copy className="mr-2 h-4 w-4" />
              Copy ID
            </Button>
            <Button asChild>
              <Link href={`/admin/transactions/${transaction._id}/edit`}>
                Edit Transaction
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Transaction Overview */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              Transaction Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Type</p>
                <Badge className={getTypeColor(transaction.type)}>
                  {transaction.type?.replace(/_/g, " ").toUpperCase()}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Status</p>
                <Badge className={getStatusColor(transaction.status)}>
                  {transaction.status?.replace(/_/g, " ").toUpperCase()}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Created</p>
                <p className="text-sm">
                  {transaction.createdAt
                    ? format(new Date(transaction.createdAt), "PPP")
                    : "-"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Processed</p>
                <p className="text-sm">
                  {transaction.processedAt
                    ? format(new Date(transaction.processedAt), "PPP")
                    : "Not processed"}
                </p>
              </div>
            </div>
            
            {transaction.description && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Description</p>
                <p className="text-sm">{transaction.description}</p>
              </div>
            )}
            
            {transaction.internalNotes && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Internal Notes</p>
                <p className="text-sm">{transaction.internalNotes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Financial Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="mr-2 h-5 w-5" />
              Financial Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Amount</p>
              <p className="text-2xl font-bold">
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: transaction.currency || "USD",
                }).format(transaction.amount)}
              </p>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Original Amount</span>
                <span className="text-sm">
                  {new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: transaction.currency || "USD",
                  }).format(transaction.originalAmount)}
                </span>
              </div>
              
              {transaction.discountAmount > 0 && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Discount</span>
                  <span className="text-sm text-green-600">
                    -{new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: transaction.currency || "USD",
                    }).format(transaction.discountAmount)}
                  </span>
                </div>
              )}
              
              <div className="flex justify-between font-medium">
                <span className="text-sm">Final Amount</span>
                <span className="text-sm">
                  {new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: transaction.currency || "USD",
                  }).format(transaction.amount)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="mr-2 h-5 w-5" />
              Payment Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Payment Method</p>
              <p className="text-sm capitalize">
                {transaction.paymentMethod?.replace(/_/g, " ")}
              </p>
            </div>
            
            {transaction.paymentGateway && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Payment Gateway</p>
                <p className="text-sm capitalize">{transaction.paymentGateway}</p>
              </div>
            )}
            
            {transaction.gatewayTransactionId && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Gateway Transaction ID</p>
                <p className="text-sm font-mono">{transaction.gatewayTransactionId}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              User Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">User ID</p>
              <p className="text-sm font-mono">{transaction.user}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-muted-foreground">Plan ID</p>
              <p className="text-sm font-mono">{transaction.plan}</p>
            </div>
            
            {transaction.coupon && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Coupon ID</p>
                <p className="text-sm font-mono">{transaction.coupon}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Refund Details */}
        {transaction.refund?.isRefunded && (
          <Card>
            <CardHeader>
              <CardTitle>Refund Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Refund Amount</p>
                <p className="text-sm">
                  {new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: transaction.currency || "USD",
                  }).format(transaction.refund.refundAmount || 0)}
                </p>
              </div>
              
              {transaction.refund.refundReason && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Refund Reason</p>
                  <p className="text-sm">{transaction.refund.refundReason}</p>
                </div>
              )}
              
              {transaction.refund.refundDate && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Refund Date</p>
                  <p className="text-sm">
                    {format(new Date(transaction.refund.refundDate), "PPP")}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </Container>
  )
}

export default TransactionDetailPage
