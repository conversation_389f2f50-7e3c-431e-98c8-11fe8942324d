"use client"

import { useMutation } from "@tanstack/react-query"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { createTransaction } from "@/actions/transactions"
import { TransactionForm } from "@/components/forms/transaction-form"
import { Button } from "@/components/ui/button"
import { Container } from "@/components/layout"
import { TransactionFormData } from "@/types/transaction"

const NewTransactionPage = () => {
  const router = useRouter()

  const createTransactionMutation = useMutation({
    mutationFn: createTransaction,
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Transaction created successfully!")
        router.push(`/admin/transactions/${data.data?._id}`)
      } else {
        toast.error(data.message || "Failed to create transaction")
      }
    },
    onError: (error) => {
      console.error("Error creating transaction:", error)
      toast.error("Failed to create transaction")
    },
  })

  const handleSubmit = (data: TransactionFormData) => {
    createTransactionMutation.mutate(data)
  }

  return (
    <Container className="py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/admin/transactions">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Transactions
          </Link>
        </Button>
        
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Transaction</h1>
          <p className="text-muted-foreground">
            Add a new transaction to the system
          </p>
        </div>
      </div>

      <div className="max-w-4xl">
        <TransactionForm onSubmit={handleSubmit} />
      </div>
    </Container>
  )
}

export default NewTransactionPage
