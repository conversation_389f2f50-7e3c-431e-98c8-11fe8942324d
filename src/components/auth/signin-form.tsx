"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { EyeIcon, EyeOffIcon } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

import { TextInput } from "@/components/forms/common"
import Icons from "@/components/global/icons"
import { Button } from "@/components/ui/button"
import LoadingIcon from "@/components/ui/loading-icon"
import { FADE_IN_VARIANTS } from "@/constants"
import { SignInSchema, SignInSchemaType } from "@/schema/signin-schema"
import { useAuth } from "@/stores/auth-store"

const SignInForm = () => {
  const router = useRouter()
  const { login, googleLogin } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
  } = useForm<SignInSchemaType>({
    resolver: zodResolver(SignInSchema),
    defaultValues: { email: "", password: "" },
  })

  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true)
    try {
      googleLogin()
      toast.loading("Redirecting to Google...")
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsGoogleLoading(false)
    }
  }

  const handleLogin = async (data: SignInSchemaType) => {
    try {
      const result = await login(data.email, data.password)
      if (result.success) {
        toast.success("Login successful!")
        router.push("/")
      } else {
        toast.error(result.message || "Login failed. Please try again.")
      }
    } catch (error) {
      console.error("Login error:", error)
      toast.error("An error occurred. Please try again.")
    }
  }

  return (
    <div className='flex w-full flex-col text-center'>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
      >
        <div className='flex justify-center'>
          <Link href='/'>
            <Icons.icon className='h-8 w-8' />
          </Link>
        </div>
        <h1 className='mt-4 text-center text-2xl'>Login to DesignByte</h1>
        <p className='mt-2 text-sm text-muted-foreground'>
          Enter your email and password to continue
        </p>
      </motion.div>

      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
        className='flex flex-col gap-4 py-8'
      >
        <form onSubmit={handleSubmit(handleLogin)} className='space-y-4'>
          <div className='space-y-2'>
            <TextInput
              control={control}
              name='email'
              label='Email'
              type='email'
              placeholder='Email'
              required
            />
            <div className='relative'>
              <TextInput
                control={control}
                name='password'
                label='Password'
                type={showPassword ? "text" : "password"}
                placeholder='Password'
                required
                className='pr-10'
              />
              <button
                type='button'
                onClick={() => setShowPassword(!showPassword)}
                className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOffIcon className='h-4 w-4' />
                ) : (
                  <EyeIcon className='h-4 w-4' />
                )}
              </button>
            </div>
          </div>
          <Button type='submit' className='w-full' disabled={isSubmitting}>
            {isSubmitting ? <LoadingIcon className='mr-2 h-4 w-4' /> : null}
            Sign In
          </Button>
        </form>

        <div className='text-center'>
          <Link
            href='/auth/forgot-password'
            className='text-sm text-muted-foreground hover:text-foreground'
          >
            Forgot your password?
          </Link>
        </div>

        <div className='relative'>
          <div className='absolute inset-0 flex items-center'>
            <span className='w-full border-t' />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background px-2 text-muted-foreground'>
              Or continue with
            </span>
          </div>
        </div>

        <Button
          variant='outline'
          onClick={handleGoogleAuth}
          disabled={isGoogleLoading}
          className='w-full'
        >
          {isGoogleLoading ? (
            <LoadingIcon className='mr-2 h-4 w-4' />
          ) : (
            <Icons.google className='mr-2 h-4 w-4' />
          )}
          Google
        </Button>
      </motion.div>

      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
        className='mt-4'
      >
        <p className='text-sm text-muted-foreground'>
          Don&apos;t have an account?{" "}
          <Link
            href='/auth/signup'
            className='font-medium text-primary hover:underline'
          >
            Sign up
          </Link>
        </p>
      </motion.div>
    </div>
  )
}

export default SignInForm
