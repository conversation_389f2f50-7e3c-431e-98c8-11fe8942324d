"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { EyeIcon, EyeOffIcon } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

import { TextInput } from "@/components/forms/common"
import { FADE_IN_VARIANTS } from "@/constants"
import { SignUpSchema, SignUpSchemaType, signUpDefaultValues } from "@/schema"
import { useAuth } from "@/stores/auth-store"

import Icons from "../global/icons"
import { Button } from "../ui/button"
import LoadingIcon from "../ui/loading-icon"

const SignUpForm = () => {
  const router = useRouter()
  const { register, googleLogin } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
  } = useForm<SignUpSchemaType>({
    resolver: zodResolver(SignUpSchema),
    defaultValues: signUpDefaultValues,
  })

  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true)
    try {
      googleLogin()
      toast.loading("Redirecting to Google...")
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsGoogleLoading(false)
    }
  }

  const handleRegister = async (data: SignUpSchemaType) => {
    try {
      const result = await register(data)
      if (result.success) {
        toast.success(
          result.message ||
            "Registration successful! Please check your email to verify your account.",
        )
        router.push("/auth/signin")
      } else {
        toast.error(result.message || "Registration failed. Please try again.")
      }
    } catch (error) {
      console.error("Registration error:", error)
      toast.error("An error occurred. Please try again.")
    }
  }

  return (
    <div className='flex w-full flex-col text-center'>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
      >
        <div className='flex justify-center'>
          <Link href='/'>
            <Icons.icon className='h-8 w-8' />
          </Link>
        </div>
        <h1 className='mt-4 text-center text-2xl'>Create your account</h1>
        <p className='mt-2 text-sm text-muted-foreground'>
          Fill in your details to create your account
        </p>
      </motion.div>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
        className='flex flex-col gap-4 py-8'
      >
        <form onSubmit={handleSubmit(handleRegister)} className='space-y-4'>
          <div className='space-y-2'>
            <TextInput
              control={control}
              name='email'
              label='Email'
              type='email'
              placeholder='Email'
              required
            />
            <TextInput
              control={control}
              name='firstName'
              label='First Name'
              type='text'
              placeholder='First Name'
              required
            />
            <TextInput
              control={control}
              name='lastName'
              label='Last Name'
              type='text'
              placeholder='Last Name'
              required
            />
            <TextInput
              control={control}
              name='username'
              label='Username'
              type='text'
              placeholder='Username'
              required
            />
            <div className='relative'>
              <TextInput
                control={control}
                name='password'
                label='Password'
                type={showPassword ? "text" : "password"}
                placeholder='Password'
                required
                className='pr-10'
              />
              <button
                type='button'
                onClick={() => setShowPassword(!showPassword)}
                className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOffIcon className='h-4 w-4' />
                ) : (
                  <EyeIcon className='h-4 w-4' />
                )}
              </button>
            </div>
            <div className='relative'>
              <TextInput
                control={control}
                name='confirmPassword'
                label='Confirm Password'
                type={showConfirmPassword ? "text" : "password"}
                placeholder='Confirm Password'
                required
                className='pr-10'
              />
              <button
                type='button'
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
                tabIndex={-1}
              >
                {showConfirmPassword ? (
                  <EyeOffIcon className='h-4 w-4' />
                ) : (
                  <EyeIcon className='h-4 w-4' />
                )}
              </button>
            </div>
          </div>
          <Button type='submit' className='w-full' disabled={isSubmitting}>
            {isSubmitting ? <LoadingIcon className='mr-2 h-4 w-4' /> : null}
            Create Account
          </Button>
        </form>

        <div className='relative'>
          <div className='absolute inset-0 flex items-center'>
            <span className='w-full border-t' />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background px-2 text-muted-foreground'>
              Or continue with
            </span>
          </div>
        </div>

        <Button
          variant='outline'
          onClick={handleGoogleAuth}
          disabled={isGoogleLoading}
          className='w-full'
        >
          {isGoogleLoading ? (
            <LoadingIcon className='mr-2 h-4 w-4' />
          ) : (
            <Icons.google className='mr-2 h-4 w-4' />
          )}
          Google
        </Button>
      </motion.div>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
        className='mt-4'
      >
        <p className='text-sm text-muted-foreground'>
          Already have an account?{" "}
          <Link
            href='/auth/signin'
            className='font-medium text-primary hover:underline'
          >
            Sign in
          </Link>
        </p>
      </motion.div>
      )
    </div>
  )
}

export default SignUpForm
