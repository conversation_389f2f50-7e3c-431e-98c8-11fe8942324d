import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { cn } from "@/functions"

type Props = {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  label: string
  sublabel?: string
  description?: string
}

export default function CustomCheckbox({
  checked,
  onCheckedChange,
  label,
  sublabel,
  description,
}: Props) {
  return (
    <div className='relative flex w-full items-start gap-2 rounded-lg border border-input p-4 shadow-sm shadow-black/5 has-[[data-state=checked]]:border-ring'>
      <Checkbox
        id='checkbox-13'
        className='order-1 after:absolute after:inset-0'
        aria-describedby='checkbox-13-description'
        checked={checked}
        onCheckedChange={onCheckedChange}
      />
      <div className='grid grow gap-2'>
        <Label htmlFor='checkbox-13'>
          {label}
          {sublabel && (
            <span className='text-xs font-normal leading-[inherit] text-muted-foreground'>
              ({sublabel})
            </span>
          )}
        </Label>
        <p
          id='checkbox-13-description'
          className={cn(
            "text-xs text-muted-foreground",
            description ? "" : "hidden",
          )}
        >
          {description}
        </p>
      </div>
    </div>
  )
}
