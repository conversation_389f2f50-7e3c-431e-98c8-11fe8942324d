// components/form/DatePicker.tsx
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Control, FieldValues, Path } from "react-hook-form"

import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface DatePickerProps<T extends FieldValues> {
  control: Control<T>
  name: Path<T>
  label: string
  disabled?: boolean
  fromDate?: Date
  className?: string
  required?: boolean
}

export const DatePicker = <T extends FieldValues>({
  control,
  name,
  label,
  disabled,
  fromDate,
  className,
  required,
}: DatePickerProps<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={cn(className)}>
        <FormLabel>
          {label}
          {required && <span className='text-red-500'>*</span>}
        </FormLabel>
        <Popover>
          <PopoverTrigger asChild>
            <FormControl>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full pl-3 text-left font-normal",
                  !field.value && "text-muted-foreground",
                )}
                disabled={disabled}
              >
                <CalendarIcon className='mr-2 h-4 w-4' />
                {field.value ? (
                  format(new Date(field.value), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </FormControl>
          </PopoverTrigger>
          <PopoverContent className='w-auto p-0' align='start'>
            <Calendar
              mode='single'
              selected={field.value ? new Date(field.value) : undefined}
              onSelect={(date) => field.onChange(date?.toISOString())}
              initialFocus
              disabled={(date) =>
                disabled || (fromDate ? date < fromDate : false)
              }
              fromDate={fromDate}
            />
          </PopoverContent>
        </Popover>
        <FormMessage />
      </FormItem>
    )}
  />
)
