// components/form/TextInput.tsx
import { Control, FieldValues, Path } from "react-hook-form"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface TextInputProps<T extends FieldValues> {
  control: Control<T>
  name: Path<T>
  label: string
  type?: string
  placeholder?: string
  className?: string
  required?: boolean
}

export const TextInput = <T extends FieldValues>({
  control,
  name,
  label,
  type,
  placeholder,
  required,
  className,
}: TextInputProps<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={cn("relative", className)}>
        <FormLabel>
          {label}
          {required && <span className='text-red-500'>*</span>}
        </FormLabel>

        <FormControl>
          <Input type={type} placeholder={placeholder} {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)
