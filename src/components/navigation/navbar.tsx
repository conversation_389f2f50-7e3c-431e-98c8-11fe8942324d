"use client"

import { motion } from "framer-motion"
import { ArrowRightIcon, LogOutIcon, UserIcon, XIcon } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

import { <PERSON>rap<PERSON> } from "@/components/layout"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/functions"
import { useAuth } from "@/stores/auth-store"

import { Icons } from "../global"
import { Menu } from "./menu"
import { MobileMenu } from "./mobile-menu"

// Custom user button component
const UserButton = () => {
  const { user, logout } = useAuth()

  if (!user) return null

  const handleLogout = async () => {
    await logout()
  }

  const getInitials = (
    firstName?: string,
    lastName?: string,
    email?: string,
  ) => {
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase()
    }
    if (firstName) {
      return firstName[0].toUpperCase()
    }
    if (email) {
      return email[0].toUpperCase()
    }
    return "U"
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='relative h-8 w-8 rounded-full'>
          <Avatar className='h-8 w-8'>
            <AvatarImage src={user.profileImage} alt={user.email} />
            <AvatarFallback>
              {getInitials(user.firstName, user.lastName, user.email)}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-56' align='end' forceMount>
        <div className='flex items-center justify-start gap-2 p-2'>
          <div className='flex flex-col space-y-1 leading-none'>
            {user.firstName && user.lastName && (
              <p className='font-medium'>
                {user.firstName} {user.lastName}
              </p>
            )}
            <p className='w-[200px] truncate text-sm text-muted-foreground'>
              {user.email}
            </p>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href='/dashboard' className='flex items-center'>
            <UserIcon className='mr-2 h-4 w-4' />
            Dashboard
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} className='flex items-center'>
          <LogOutIcon className='mr-2 h-4 w-4' />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export const excludesUrls = ["/templates", "/dashboard", "/login", "/signup"]
export const includesUrls = [
  "/",
  "/pricing",
  "/contact-us",
  "/about-us",
  "/blogs",
]

export const Navbar = () => {
  const { isAuthenticated } = useAuth()

  const pathname = usePathname()
  const [showNavbar, setShowNavbar] = useState<boolean>(false)

  const [isOpen, setIsOpen] = useState<boolean>(false)

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = ""
    }

    return () => {
      document.body.style.overflow = ""
    }
  }, [isOpen])

  useEffect(() => {
    // if (excludesUrls?.some((url) => pathname.startsWith(url))) {
    //   setShowNavbar(false);
    // } else {
    //   setShowNavbar(true);
    // }
    if (includesUrls?.some((url) => pathname === url)) {
      setShowNavbar(true)
    } else {
      setShowNavbar(false)
    }
  }, [pathname])

  // if (excludesUrls?.some((url) => pathname.startsWith(url))) {
  //   return;
  // }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: showNavbar ? 1 : 0 }}
      transition={{ duration: 0.5 }}
      className={cn("relative h-full w-full")}
      // className={cn("relative w-full h-full hidden", showNavbar && "block")}
    >
      {/* <div className={cn("relative w-full h-full", !showNavbar && "opacity-0")}> */}
      <div className='pointer-events-none fixed inset-x-0 z-[99] h-[88px] bg-[rgba(10,10,10,0.8)] backdrop-blur-sm [mask:linear-gradient(to_bottom,#000_20%,transparent_calc(100%-20%))]' />

      <header
        className={cn(
          "th fixed inset-x-0 top-4 z-[100] mx-auto max-w-7xl transform px-2 md:px-12",
          isOpen ? "h-[calc(100%-24px)]" : "h-12",
        )}
      >
        <Wrapper className='px- flex items-center justify-start rounded-xl border border-[rgba(124,124,124,0.2)] backdrop-blur-lg md:px-2 lg:rounded-2xl'>
          <div className='sticky inset-x-0 mb-auto mt-[7px] flex w-full items-center justify-between lg:mt-auto'>
            <div className='flex flex-1 items-center pl-1 lg:flex-none'>
              <Link
                href='/'
                className='flex items-center gap-2 text-lg font-semibold text-foreground'
              >
                <Icons.icon className='h-5 w-auto' />
                <span className='text-lg font-semibold'>DesignByte</span>
              </Link>
              {/* <div className="items-center hidden ml-4 lg:flex">
                <Menu />
              </div> */}
            </div>

            <div className='ml-4 hidden items-center lg:flex'>
              <Menu />
            </div>

            <div className='flex items-center gap-2 lg:gap-4'>
              {isAuthenticated ? (
                <>
                  <Button
                    size='sm'
                    variant='default'
                    asChild
                    className='hidden sm:flex'
                  >
                    <Link href='/dashboard'>Dashboard</Link>
                  </Button>
                  <UserButton />
                </>
              ) : (
                <>
                  <Button
                    size='sm'
                    variant='tertiary'
                    asChild
                    className='hover:translate-y-0 hover:scale-100'
                  >
                    <Link href='/auth/signin'>Login</Link>
                  </Button>
                  <Button
                    size='sm'
                    variant='default'
                    asChild
                    className='hidden sm:flex'
                  >
                    <Link href='/templates'>
                      Start for free
                      <ArrowRightIcon className='ml-2 hidden h-4 w-4 lg:block' />
                    </Link>
                  </Button>
                </>
              )}

              <Button
                size='icon'
                variant='ghost'
                onClick={() => setIsOpen((prev) => !prev)}
                className='h-8 w-8 p-2 lg:hidden'
              >
                {isOpen ? (
                  <XIcon className='h-4 w-4 duration-300' />
                ) : (
                  <Icons.menu className='h-3.5 w-3.5 duration-300' />
                )}
              </Button>
            </div>
          </div>
          <MobileMenu isOpen={isOpen} setIsOpen={setIsOpen} />
        </Wrapper>
      </header>
    </motion.div>
  )
}
