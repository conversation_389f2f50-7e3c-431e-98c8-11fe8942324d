import { z } from "zod"

const regex = {
  password:
    /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]).{8,}$/,
}

export const SignUpSchema = z
  .object({
    email: z.string().email({
      message: "Invalid email address",
    }),
    firstName: z.string().min(2, {
      message: "First name must be at least 2 characters",
    }),
    lastName: z.string().min(2, {
      message: "Last name must be at least 2 characters",
    }),
    username: z.string().min(3, {
      message: "Username must be at least 3 characters",
    }),
    password: z
      .string()
      .min(8, {
        message: "Password must be at least 8 characters long",
      })
      .max(12, {
        message: "Password must be at most 12 characters long",
      })
      .regex(regex.password, {
        message:
          "Password must contain at least 1 letter, 1 number and 1 special character",
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })

export type SignUpSchemaType = z.infer<typeof SignUpSchema>

// Default values for the signup form
export const signUpDefaultValues: SignUpSchemaType = {
  email: "",
  firstName: "",
  lastName: "",
  username: "",
  password: "",
  confirmPassword: "",
}
