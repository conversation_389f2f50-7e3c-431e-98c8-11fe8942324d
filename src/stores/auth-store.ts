"use client"

import { useEffect } from "react"
import { create } from "zustand"
import { persist } from "zustand/middleware"

import { User } from "@/types/user"

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  token: string | null
}

export interface LoginData {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  username: string
  password: string
  firstName: string
  lastName: string
}

export interface VerifyEmailData {
  token: string
}

export interface ForgotPasswordData {
  email: string
}

export interface ResetPasswordData {
  token: string
  password: string
}

interface AuthActions {
  login: (data: LoginData) => Promise<{ success: boolean; message?: string }>
  register: (
    data: RegisterData,
  ) => Promise<{ success: boolean; message?: string }>
  logout: () => Promise<void>
  verifyEmail: (
    data: VerifyEmailData,
  ) => Promise<{ success: boolean; message?: string }>
  forgotPassword: (
    data: ForgotPasswordData,
  ) => Promise<{ success: boolean; message?: string }>
  resetPassword: (
    data: ResetPasswordData,
  ) => Promise<{ success: boolean; message?: string }>
  googleLogin: () => void
  refreshUser: () => Promise<void>
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<void>
}

type AuthStore = AuthState & AuthActions

export const useAuth = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      isLoading: true,
      token: null,
      get isAuthenticated() {
        return !!get().user
      },

      // Actions
      setUser: (user) => set({ user }),
      setToken: (token) => {
        if (token) {
          localStorage.setItem("token", token)
        } else {
          localStorage.removeItem("token")
        }
        set({ token })
      },
      setLoading: (isLoading) => set({ isLoading }),

      checkAuth: async () => {
        const { setUser, setLoading, setToken } = get()
        try {
          const token = localStorage.getItem("token")
          if (!token) {
            setLoading(false)
            return
          }

          const response = await fetch("/api/auth/me", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })

          if (response.ok) {
            const data = await response.json()
            setUser(data.user)
            setToken(token)
          } else {
            setToken(null)
            setUser(null)
          }
        } catch (error) {
          console.error("Auth check failed:", error)
          setToken(null)
          setUser(null)
        } finally {
          setLoading(false)
        }
      },

      login: async (data: LoginData) => {
        const { setUser, setToken } = get()
        try {
          const response = await fetch("/api/auth/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: data.email,
              password: data.password,
            }),
          })

          const responseData = await response.json()

          if (response.ok) {
            setToken(responseData.token)
            setUser(responseData.user)
            return { success: true }
          } else {
            return { success: false, message: responseData.message }
          }
        } catch (error) {
          console.error("Login failed:", error)
          return { success: false, message: "Login failed. Please try again." }
        }
      },

      register: async (data: RegisterData) => {
        try {
          const response = await fetch("/api/auth/register", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: data.email,
              username: data.username,
              password: data.password,
              firstName: data.firstName,
              lastName: data.lastName,
            }),
          })

          const responseData = await response.json()

          if (response.ok) {
            return {
              success: true,
              message:
                "Registration successful. Please check your email to verify your account.",
            }
          } else {
            return { success: false, message: responseData.message }
          }
        } catch (error) {
          console.error("Registration failed:", error)
          return {
            success: false,
            message: "Registration failed. Please try again.",
          }
        }
      },

      logout: async () => {
        const { setUser, setToken } = get()
        try {
          const token = localStorage.getItem("token")
          if (token) {
            await fetch("/api/auth/logout", {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            })
          }
        } catch (error) {
          console.error("Logout failed:", error)
        } finally {
          setToken(null)
          setUser(null)
        }
      },

      verifyEmail: async (data: VerifyEmailData) => {
        try {
          const response = await fetch("/api/auth/verify-email", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ token: data.token }),
          })

          const responseData = await response.json()

          if (response.ok) {
            return { success: true, message: "Email verified successfully!" }
          } else {
            return { success: false, message: responseData.message }
          }
        } catch (error) {
          console.error("Email verification failed:", error)
          return {
            success: false,
            message: "Email verification failed. Please try again.",
          }
        }
      },

      forgotPassword: async (data: ForgotPasswordData) => {
        try {
          const response = await fetch("/api/auth/forgot-password", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email: data.email }),
          })

          const responseData = await response.json()

          if (response.ok) {
            return {
              success: true,
              message: "Password reset email sent. Please check your inbox.",
            }
          } else {
            return { success: false, message: responseData.message }
          }
        } catch (error) {
          console.error("Forgot password failed:", error)
          return {
            success: false,
            message: "Failed to send reset email. Please try again.",
          }
        }
      },

      resetPassword: async (data: ResetPasswordData) => {
        try {
          const response = await fetch("/api/auth/reset-password", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              token: data.token,
              password: data.password,
            }),
          })

          const responseData = await response.json()

          if (response.ok) {
            return { success: true, message: "Password reset successfully!" }
          } else {
            return { success: false, message: responseData.message }
          }
        } catch (error) {
          console.error("Password reset failed:", error)
          return {
            success: false,
            message: "Password reset failed. Please try again.",
          }
        }
      },

      googleLogin: () => {
        window.location.href = "/api/auth/google"
      },

      refreshUser: async () => {
        const { checkAuth } = get()
        await checkAuth()
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        token: state.token,
        user: state.user,
      }),
    },
  ),
)

// Custom hook for auth initialization
export const useAuthInit = () => {
  const { checkAuth, isLoading } = useAuth()

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return { isLoading }
}

// Initialize auth check on app start
if (typeof window !== "undefined") {
  useAuth.getState().checkAuth()
}
